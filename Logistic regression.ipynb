{"cells": [{"cell_type": "code", "execution_count": 37, "id": "1e0bc1e6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.pipeline import Pipeline"]}, {"cell_type": "code", "execution_count": 2, "id": "ee7e7392", "metadata": {}, "outputs": [], "source": ["file_path = \"australian-weather-prediction\\weatherAUS_train.csv\"\n", "df = pd.read_csv(file_path)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "55c306d6", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Date", "rawType": "object", "type": "string"}, {"name": "Location", "rawType": "object", "type": "string"}, {"name": "MinTemp", "rawType": "float64", "type": "float"}, {"name": "MaxTemp", "rawType": "float64", "type": "float"}, {"name": "Rainfall", "rawType": "float64", "type": "float"}, {"name": "Evaporation", "rawType": "float64", "type": "float"}, {"name": "Sunshine", "rawType": "float64", "type": "float"}, {"name": "WindGustDir", "rawType": "object", "type": "string"}, {"name": "WindGustSpeed", "rawType": "float64", "type": "float"}, {"name": "WindDir9am", "rawType": "object", "type": "string"}, {"name": "WindDir3pm", "rawType": "object", "type": "string"}, {"name": "WindSpeed9am", "rawType": "float64", "type": "float"}, {"name": "WindSpeed3pm", "rawType": "float64", "type": "float"}, {"name": "Humidity9am", "rawType": "float64", "type": "float"}, {"name": "Humidity3pm", "rawType": "float64", "type": "float"}, {"name": "Pressure9am", "rawType": "float64", "type": "float"}, {"name": "Pressure3pm", "rawType": "float64", "type": "float"}, {"name": "Cloud9am", "rawType": "float64", "type": "float"}, {"name": "Cloud3pm", "rawType": "float64", "type": "float"}, {"name": "Temp9am", "rawType": "float64", "type": "float"}, {"name": "Temp3pm", "rawType": "float64", "type": "float"}, {"name": "RainToday", "rawType": "object", "type": "string"}, {"name": "RainTomorrow", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "d267409f-dc8c-4ba8-a22d-f5f0bf8f5c2e", "rows": [["0", "2013-07-22", "Sydney", "8.7", "17.2", "0.0", "3.8", "9.9", "WNW", "54.0", "WNW", "W", "17.0", "26.0", "54.0", "28.0", "1019.4", "1016.3", "2.0", "2.0", "12.2", "16.6", "No", "0"], ["1", "2015-02-28", "Sale", "15.4", "25.7", "0.0", null, null, "W", "67.0", "NNE", "WSW", "11.0", "9.0", "84.0", "66.0", "1008.1", "1005.1", "8.0", "8.0", "18.8", "24.3", "No", "1"], ["2", "2009-11-18", "PerthAirport", "15.2", "22.2", "7.2", "5.2", "3.3", "NW", "78.0", "NW", "WSW", "33.0", "28.0", "68.0", "91.0", "1001.5", "1000.1", "7.0", "8.0", "20.1", "16.5", "Yes", "1"], ["3", "2014-02-19", "Richmond", "19.9", "26.4", "0.0", "2.8", null, "SW", "24.0", "NNE", "WSW", "13.0", "9.0", "97.0", "97.0", "1004.6", "1002.1", null, null, "22.6", "22.3", "No", "1"], ["4", "2015-10-04", "Wollongong", "19.3", "32.9", "0.0", null, null, "W", "48.0", "NNW", "W", "19.0", "22.0", "46.0", "15.0", "1022.8", "1020.7", null, null, "22.1", "32.6", "No", "0"]], "shape": {"columns": 23, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Location</th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>Rainfall</th>\n", "      <th>Evaporation</th>\n", "      <th><PERSON></th>\n", "      <th>WindGustDir</th>\n", "      <th>WindGustSpeed</th>\n", "      <th>WindDir9am</th>\n", "      <th>...</th>\n", "      <th>Humidity9am</th>\n", "      <th>Humidity3pm</th>\n", "      <th>Pressure9am</th>\n", "      <th>Pressure3pm</th>\n", "      <th>Cloud9am</th>\n", "      <th>Cloud3pm</th>\n", "      <th>Temp9am</th>\n", "      <th>Temp3pm</th>\n", "      <th><PERSON><PERSON><PERSON>y</th>\n", "      <th>RainTomorrow</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2013-07-22</td>\n", "      <td>Sydney</td>\n", "      <td>8.7</td>\n", "      <td>17.2</td>\n", "      <td>0.0</td>\n", "      <td>3.8</td>\n", "      <td>9.9</td>\n", "      <td>WNW</td>\n", "      <td>54.0</td>\n", "      <td>WNW</td>\n", "      <td>...</td>\n", "      <td>54.0</td>\n", "      <td>28.0</td>\n", "      <td>1019.4</td>\n", "      <td>1016.3</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>12.2</td>\n", "      <td>16.6</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2015-02-28</td>\n", "      <td>Sale</td>\n", "      <td>15.4</td>\n", "      <td>25.7</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>W</td>\n", "      <td>67.0</td>\n", "      <td>NNE</td>\n", "      <td>...</td>\n", "      <td>84.0</td>\n", "      <td>66.0</td>\n", "      <td>1008.1</td>\n", "      <td>1005.1</td>\n", "      <td>8.0</td>\n", "      <td>8.0</td>\n", "      <td>18.8</td>\n", "      <td>24.3</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2009-11-18</td>\n", "      <td>PerthAirport</td>\n", "      <td>15.2</td>\n", "      <td>22.2</td>\n", "      <td>7.2</td>\n", "      <td>5.2</td>\n", "      <td>3.3</td>\n", "      <td>NW</td>\n", "      <td>78.0</td>\n", "      <td>NW</td>\n", "      <td>...</td>\n", "      <td>68.0</td>\n", "      <td>91.0</td>\n", "      <td>1001.5</td>\n", "      <td>1000.1</td>\n", "      <td>7.0</td>\n", "      <td>8.0</td>\n", "      <td>20.1</td>\n", "      <td>16.5</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2014-02-19</td>\n", "      <td>Richmond</td>\n", "      <td>19.9</td>\n", "      <td>26.4</td>\n", "      <td>0.0</td>\n", "      <td>2.8</td>\n", "      <td>NaN</td>\n", "      <td>SW</td>\n", "      <td>24.0</td>\n", "      <td>NNE</td>\n", "      <td>...</td>\n", "      <td>97.0</td>\n", "      <td>97.0</td>\n", "      <td>1004.6</td>\n", "      <td>1002.1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>22.6</td>\n", "      <td>22.3</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2015-10-04</td>\n", "      <td>Wollongong</td>\n", "      <td>19.3</td>\n", "      <td>32.9</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>W</td>\n", "      <td>48.0</td>\n", "      <td>NNW</td>\n", "      <td>...</td>\n", "      <td>46.0</td>\n", "      <td>15.0</td>\n", "      <td>1022.8</td>\n", "      <td>1020.7</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>22.1</td>\n", "      <td>32.6</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["         Date      Location  MinTemp  MaxTemp  Rainfall  Evaporation  \\\n", "0  2013-07-22        Sydney      8.7     17.2       0.0          3.8   \n", "1  2015-02-28          Sale     15.4     25.7       0.0          NaN   \n", "2  2009-11-18  PerthAirport     15.2     22.2       7.2          5.2   \n", "3  2014-02-19      Richmond     19.9     26.4       0.0          2.8   \n", "4  2015-10-04    Wollongong     19.3     32.9       0.0          NaN   \n", "\n", "   Sunshine WindGustDir  WindGustSpeed WindDir9am  ... Humidity9am  \\\n", "0       9.9         WNW           54.0        WNW  ...        54.0   \n", "1       NaN           W           67.0        NNE  ...        84.0   \n", "2       3.3          NW           78.0         NW  ...        68.0   \n", "3       NaN          SW           24.0        NNE  ...        97.0   \n", "4       NaN           W           48.0        NNW  ...        46.0   \n", "\n", "   Humidity3pm  Pressure9am  Pressure3pm  Cloud9am  Cloud3pm  Temp9am  \\\n", "0         28.0       1019.4       1016.3       2.0       2.0     12.2   \n", "1         66.0       1008.1       1005.1       8.0       8.0     18.8   \n", "2         91.0       1001.5       1000.1       7.0       8.0     20.1   \n", "3         97.0       1004.6       1002.1       NaN       NaN     22.6   \n", "4         15.0       1022.8       1020.7       NaN       NaN     22.1   \n", "\n", "   Temp3pm  RainToday  RainTomorrow  \n", "0     16.6         No             0  \n", "1     24.3         No             1  \n", "2     16.5        Yes             1  \n", "3     22.3         No             1  \n", "4     32.6         No             0  \n", "\n", "[5 rows x 23 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "2e0256a0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<bound method NDFrame.describe of               Date      Location  MinTemp  MaxTemp  Rainfall  Evaporation  \\\n", "0       2013-07-22        Sydney      8.7     17.2       0.0          3.8   \n", "1       2015-02-28          Sale     15.4     25.7       0.0          NaN   \n", "2       2009-11-18  PerthAirport     15.2     22.2       7.2          5.2   \n", "3       2014-02-19      Richmond     19.9     26.4       0.0          2.8   \n", "4       2015-10-04    Wollongong     19.3     32.9       0.0          NaN   \n", "...            ...           ...      ...      ...       ...          ...   \n", "101817  2015-07-19       Walpole     11.6     17.0       3.0          NaN   \n", "101818  2015-06-25  PerthAirport      7.6     20.4       0.0          2.4   \n", "101819  2016-06-05     Newcastle     13.8     17.3      96.0          NaN   \n", "101820  2011-06-13      Richmond     10.5     14.6       4.0          NaN   \n", "101821  2010-09-11     Newcastle      8.2     19.8       0.6          NaN   \n", "\n", "        Sunshine WindGustDir  WindGustSpeed WindDir9am  ... Humidity9am  \\\n", "0            9.9         WNW           54.0        WNW  ...        54.0   \n", "1            NaN           W           67.0        NNE  ...        84.0   \n", "2            3.3          NW           78.0         NW  ...        68.0   \n", "3            NaN          SW           24.0        NNE  ...        97.0   \n", "4            NaN           W           48.0        NNW  ...        46.0   \n", "...          ...         ...            ...        ...  ...         ...   \n", "101817       NaN         NNW           50.0        NNW  ...        88.0   \n", "101818       9.2         ENE           30.0         NE  ...        64.0   \n", "101819       NaN         NaN            NaN        NaN  ...       100.0   \n", "101820       NaN           S           33.0        SSW  ...        82.0   \n", "101821       NaN         NaN            NaN          N  ...        50.0   \n", "\n", "        Humidity3pm  Pressure9am  Pressure3pm  Cloud9am  Cloud3pm  Temp9am  \\\n", "0              28.0       1019.4       1016.3       2.0       2.0     12.2   \n", "1              66.0       1008.1       1005.1       8.0       8.0     18.8   \n", "2              91.0       1001.5       1000.1       7.0       8.0     20.1   \n", "3              97.0       1004.6       1002.1       NaN       NaN     22.6   \n", "4              15.0       1022.8       1020.7       NaN       NaN     22.1   \n", "...             ...          ...          ...       ...       ...      ...   \n", "101817         90.0       1013.2       1008.9       NaN       NaN     14.0   \n", "101818         35.0       1027.7       1024.2       0.0       1.0     12.1   \n", "101819          NaN          NaN          NaN       8.0       NaN     14.5   \n", "101820         90.0       1024.6       1021.6       NaN       NaN     12.2   \n", "101821         51.0          NaN          NaN       1.0       1.0     16.6   \n", "\n", "        Temp3pm  RainToday  RainTomorrow  \n", "0          16.6         No             0  \n", "1          24.3         No             1  \n", "2          16.5        Yes             1  \n", "3          22.3         No             1  \n", "4          32.6         No             0  \n", "...         ...        ...           ...  \n", "101817     15.0        Yes             1  \n", "101818     19.9         No             0  \n", "101819      NaN        Yes             1  \n", "101820     13.9        Yes             1  \n", "101821     18.0         No             0  \n", "\n", "[101822 rows x 23 columns]>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe"]}, {"cell_type": "code", "execution_count": 12, "id": "57774dbc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Date', 'Location', '<PERSON><PERSON>em<PERSON>', '<PERSON><PERSON>em<PERSON>', 'Rainfall', 'Evaporation', 'Sunshine', 'WindGustDir', 'WindGustSpeed', 'WindDir9am', 'WindDir3pm', 'WindSpeed9am', 'WindSpeed3pm', 'Humidity9am', 'Humidity3pm', 'Pressure9am', 'Pressure3pm', 'Cloud9am', 'Cloud3pm', 'Temp9am', 'Temp3pm', 'RainToday', 'RainTomorrow']\n"]}], "source": ["print(df.columns.tolist())"]}, {"cell_type": "code", "execution_count": 19, "id": "00d59f72", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'raintomorrow')"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8,5))\n", "plt.subplot(4,4,4)\n", "fig = df.Rainfall.hist(bins=50)\n", "fig.set_xlabel('MinTemp')\n", "fig.set_ylabel('raintomorrow')"]}, {"cell_type": "code", "execution_count": 20, "id": "e6a2ff05", "metadata": {}, "outputs": [{"data": {"text/plain": ["Date                 0\n", "Location             0\n", "MinTemp           1027\n", "MaxTemp            875\n", "Rainfall          2300\n", "Evaporation      43889\n", "Sunshine         48800\n", "WindGustDir       7318\n", "WindGustSpeed     7272\n", "WindDir9am        7391\n", "WindDir3pm        2986\n", "WindSpeed9am      1272\n", "WindSpeed3pm      2159\n", "Humidity9am       1854\n", "Humidity3pm       3160\n", "Pressure9am      10554\n", "Pressure3pm      10518\n", "Cloud9am         38989\n", "Cloud3pm         41420\n", "Temp9am           1226\n", "Temp3pm           2518\n", "RainToday         2300\n", "RainTomorrow         0\n", "dtype: int64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 23, "id": "72842a3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Date             0\n", "Location         0\n", "MinTemp          0\n", "MaxTemp          0\n", "Rainfall         0\n", "Evaporation      0\n", "Sunshine         0\n", "WindGustDir      0\n", "WindGustSpeed    0\n", "WindDir9am       0\n", "WindDir3pm       0\n", "WindSpeed9am     0\n", "WindSpeed3pm     0\n", "Humidity9am      0\n", "Humidity3pm      0\n", "Pressure9am      0\n", "Pressure3pm      0\n", "Cloud9am         0\n", "Cloud3pm         0\n", "Temp9am          0\n", "Temp3pm          0\n", "RainToday        0\n", "RainTomorrow     0\n", "dtype: int64\n"]}, {"data": {"text/plain": ["(39716, 23)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df_clean = df.dropna()\n", "print(df_clean.isnull().sum())\n", "df_clean.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "53fe4d57", "metadata": {}, "outputs": [{"data": {"text/plain": ["Date              object\n", "Location          object\n", "MinTemp          float64\n", "MaxTemp          float64\n", "Rainfall         float64\n", "Evaporation      float64\n", "Sunshine         float64\n", "WindGustDir       object\n", "WindGustSpeed    float64\n", "WindDir9am        object\n", "WindDir3pm        object\n", "WindSpeed9am     float64\n", "WindSpeed3pm     float64\n", "Humidity9am      float64\n", "Humidity3pm      float64\n", "Pressure9am      float64\n", "Pressure3pm      float64\n", "Cloud9am         float64\n", "Cloud3pm         float64\n", "Temp9am          float64\n", "Temp3pm          float64\n", "RainToday         object\n", "RainTomorrow       int64\n", "dtype: object"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": 26, "id": "0727edf0", "metadata": {}, "outputs": [{"data": {"text/plain": ["MinTemp          float64\n", "MaxTemp          float64\n", "Rainfall         float64\n", "Evaporation      float64\n", "Sunshine         float64\n", "WindGustSpeed    float64\n", "WindSpeed9am     float64\n", "WindSpeed3pm     float64\n", "Humidity9am      float64\n", "Humidity3pm      float64\n", "Pressure9am      float64\n", "Pressure3pm      float64\n", "Cloud9am         float64\n", "Cloud3pm         float64\n", "Temp9am          float64\n", "Temp3pm          float64\n", "RainTomorrow       int64\n", "dtype: object"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["numeric_df = df_clean.select_dtypes(exclude=['object'])\n", "numeric_df.dtypes"]}, {"cell_type": "code", "execution_count": 27, "id": "1ad20874", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "MinTemp", "rawType": "float64", "type": "float"}, {"name": "MaxTemp", "rawType": "float64", "type": "float"}, {"name": "Rainfall", "rawType": "float64", "type": "float"}, {"name": "Evaporation", "rawType": "float64", "type": "float"}, {"name": "Sunshine", "rawType": "float64", "type": "float"}, {"name": "WindGustSpeed", "rawType": "float64", "type": "float"}, {"name": "WindSpeed9am", "rawType": "float64", "type": "float"}, {"name": "WindSpeed3pm", "rawType": "float64", "type": "float"}, {"name": "Humidity9am", "rawType": "float64", "type": "float"}, {"name": "Humidity3pm", "rawType": "float64", "type": "float"}, {"name": "Pressure9am", "rawType": "float64", "type": "float"}, {"name": "Pressure3pm", "rawType": "float64", "type": "float"}, {"name": "Cloud9am", "rawType": "float64", "type": "float"}, {"name": "Cloud3pm", "rawType": "float64", "type": "float"}, {"name": "Temp9am", "rawType": "float64", "type": "float"}, {"name": "Temp3pm", "rawType": "float64", "type": "float"}, {"name": "RainTomorrow", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "5cf328e3-fad6-4993-8c14-d97db6519351", "rows": [["0", "8.7", "17.2", "0.0", "3.8", "9.9", "54.0", "17.0", "26.0", "54.0", "28.0", "1019.4", "1016.3", "2.0", "2.0", "12.2", "16.6", "0"], ["2", "15.2", "22.2", "7.2", "5.2", "3.3", "78.0", "33.0", "28.0", "68.0", "91.0", "1001.5", "1000.1", "7.0", "8.0", "20.1", "16.5", "1"], ["6", "-0.3", "14.2", "0.0", "3.6", "9.7", "43.0", "13.0", "28.0", "67.0", "50.0", "1019.0", "1015.5", "2.0", "0.0", "9.2", "13.7", "0"], ["13", "9.8", "20.1", "0.0", "4.8", "8.3", "41.0", "17.0", "20.0", "60.0", "47.0", "1015.3", "1013.7", "6.0", "7.0", "15.1", "18.9", "0"], ["14", "19.1", "27.2", "0.0", "6.4", "3.2", "44.0", "19.0", "30.0", "69.0", "63.0", "1016.2", "1013.5", "7.0", "7.0", "23.2", "25.1", "1"], ["15", "12.9", "33.6", "0.0", "8.4", "13.1", "46.0", "11.0", "13.0", "44.0", "11.0", "1012.1", "1006.7", "1.0", "1.0", "20.2", "32.6", "0"], ["16", "25.8", "40.1", "0.4", "12.8", "8.8", "69.0", "17.0", "24.0", "24.0", "17.0", "1008.7", "1004.5", "2.0", "2.0", "31.2", "37.9", "0"], ["19", "13.5", "21.4", "0.0", "2.2", "4.9", "39.0", "7.0", "24.0", "96.0", "64.0", "1022.0", "1020.9", "7.0", "7.0", "15.4", "19.1", "0"], ["21", "25.8", "32.0", "0.0", "8.0", "9.6", "35.0", "13.0", "22.0", "63.0", "60.0", "1013.3", "1010.4", "4.0", "6.0", "29.8", "30.6", "0"], ["23", "15.5", "25.4", "0.0", "5.6", "11.0", "43.0", "11.0", "24.0", "63.0", "50.0", "1021.5", "1017.6", "1.0", "4.0", "20.3", "24.3", "0"], ["25", "24.9", "30.4", "26.8", "5.2", "4.8", "26.0", "2.0", "11.0", "86.0", "83.0", "1004.8", "1002.0", "6.0", "7.0", "27.6", "28.6", "1"], ["26", "5.1", "15.9", "0.1", "1.8", "0.8", "31.0", "20.0", "19.0", "71.0", "63.0", "1023.4", "1020.0", "7.0", "7.0", "7.2", "11.9", "0"], ["34", "-3.0", "19.8", "0.0", "3.0", "10.5", "26.0", "9.0", "13.0", "42.0", "14.0", "1023.1", "1018.5", "0.0", "0.0", "6.5", "19.0", "0"], ["36", "10.8", "25.2", "0.0", "4.0", "3.1", "50.0", "31.0", "30.0", "65.0", "36.0", "1023.3", "1020.3", "7.0", "7.0", "19.2", "23.9", "0"], ["37", "14.6", "21.3", "2.0", "2.4", "9.4", "35.0", "13.0", "15.0", "49.0", "45.0", "1017.6", "1014.9", "6.0", "1.0", "17.1", "19.9", "0"], ["39", "23.0", "28.4", "1.0", "5.6", "1.5", "37.0", "19.0", "17.0", "80.0", "62.0", "1012.8", "1010.4", "7.0", "8.0", "23.6", "26.9", "1"], ["41", "3.5", "18.9", "0.0", "3.0", "9.9", "43.0", "15.0", "30.0", "76.0", "36.0", "1016.6", "1013.8", "0.0", "1.0", "9.2", "18.1", "0"], ["43", "4.8", "17.0", "1.4", "1.6", "11.6", "30.0", "19.0", "17.0", "58.0", "55.0", "1022.2", "1021.1", "2.0", "5.0", "12.3", "16.0", "0"], ["44", "7.9", "19.5", "0.6", "0.6", "6.9", "26.0", "13.0", "11.0", "81.0", "62.0", "1023.2", "1019.3", "4.0", "2.0", "12.4", "18.5", "0"], ["45", "4.3", "15.7", "0.0", "2.0", "6.3", "39.0", "24.0", "26.0", "67.0", "57.0", "1025.4", "1023.7", "2.0", "6.0", "12.0", "14.3", "0"], ["46", "12.5", "19.0", "0.0", "6.8", "0.7", "44.0", "19.0", "26.0", "57.0", "48.0", "1018.9", "1018.9", "7.0", "7.0", "14.5", "17.2", "0"], ["48", "5.5", "22.2", "0.3", "7.3", "12.0", "41.0", "20.0", "19.0", "45.0", "26.0", "1021.1", "1020.7", "4.0", "2.0", "14.7", "21.0", "0"], ["54", "16.2", "28.1", "0.0", "10.0", "11.5", "54.0", "15.0", "35.0", "53.0", "45.0", "1010.3", "1008.8", "4.0", "1.0", "21.5", "25.7", "0"], ["55", "12.5", "21.3", "0.0", "2.4", "5.3", "17.0", "4.0", "6.0", "92.0", "67.0", "1023.5", "1019.3", "7.0", "4.0", "14.3", "20.6", "0"], ["56", "3.7", "14.2", "0.0", "3.0", "10.0", "46.0", "24.0", "28.0", "52.0", "27.0", "1012.7", "1011.8", "1.0", "1.0", "8.7", "13.5", "0"], ["57", "24.1", "33.7", "0.0", "5.4", "8.6", "35.0", "7.0", "17.0", "80.0", "44.0", "1011.7", "1008.4", "3.0", "7.0", "28.6", "32.8", "0"], ["64", "9.6", "19.2", "0.0", "3.4", "7.2", "30.0", "9.0", "13.0", "60.0", "42.0", "1027.4", "1023.2", "5.0", "6.0", "11.6", "18.7", "0"], ["66", "26.3", "35.6", "0.0", "9.2", "11.3", "43.0", "15.0", "26.0", "71.0", "50.0", "1011.5", "1006.7", "3.0", "1.0", "29.8", "33.3", "0"], ["70", "23.4", "31.5", "0.0", "13.4", "4.2", "28.0", "6.0", "13.0", "47.0", "42.0", "1010.9", "1010.2", "8.0", "7.0", "28.0", "29.3", "0"], ["72", "21.0", "26.8", "0.0", "11.2", "9.7", "41.0", "11.0", "19.0", "64.0", "55.0", "1016.0", "1015.0", "7.0", "5.0", "22.6", "23.8", "0"], ["77", "9.1", "25.0", "0.0", "2.4", "10.5", "30.0", "13.0", "15.0", "84.0", "64.0", "1020.0", "1017.7", "2.0", "3.0", "15.0", "22.5", "0"], ["79", "12.6", "18.4", "0.0", "9.0", "7.7", "67.0", "28.0", "39.0", "54.0", "58.0", "1001.6", "1001.0", "7.0", "7.0", "16.5", "15.0", "1"], ["80", "1.0", "7.2", "9.0", "4.0", "4.3", "63.0", "35.0", "30.0", "53.0", "76.0", "999.8", "1007.7", "2.0", "5.0", "4.5", "4.8", "1"], ["81", "19.3", "27.6", "0.0", "6.6", "10.4", "46.0", "24.0", "30.0", "65.0", "54.0", "1019.8", "1017.0", "2.0", "1.0", "24.5", "26.2", "0"], ["90", "8.3", "19.4", "0.0", "5.8", "8.3", "46.0", "19.0", "24.0", "65.0", "47.0", "1018.8", "1017.5", "1.0", "4.0", "14.5", "18.4", "0"], ["91", "6.0", "21.8", "0.0", "2.6", "11.2", "39.0", "7.0", "20.0", "80.0", "51.0", "1020.3", "1017.0", "3.0", "6.0", "14.7", "21.2", "0"], ["92", "24.1", "32.3", "0.0", "8.0", "10.9", "26.0", "6.0", "9.0", "54.0", "54.0", "1014.3", "1010.7", "4.0", "3.0", "28.4", "31.1", "1"], ["98", "12.2", "25.3", "0.0", "6.2", "12.4", "24.0", "6.0", "9.0", "46.0", "38.0", "1019.4", "1014.7", "2.0", "1.0", "20.9", "24.4", "0"], ["99", "17.7", "26.9", "0.0", "6.6", "3.9", "24.0", "4.0", "9.0", "60.0", "52.0", "1014.7", "1015.6", "7.0", "7.0", "22.2", "24.4", "0"], ["101", "24.4", "36.0", "0.0", "8.0", "9.0", "57.0", "13.0", "37.0", "68.0", "48.0", "1011.1", "1009.5", "5.0", "5.0", "28.8", "32.7", "1"], ["102", "12.0", "24.4", "0.0", "2.2", "5.2", "28.0", "20.0", "19.0", "74.0", "41.0", "1023.9", "1019.8", "3.0", "7.0", "15.3", "22.6", "0"], ["106", "22.3", "32.0", "30.2", "8.0", "7.5", "46.0", "9.0", "19.0", "80.0", "67.0", "1009.5", "1006.7", "7.0", "7.0", "26.3", "31.5", "1"], ["109", "20.9", "42.3", "0.0", "11.8", "11.0", "57.0", "7.0", "22.0", "47.0", "30.0", "1014.7", "1013.3", "4.0", "4.0", "30.5", "37.3", "0"], ["110", "24.3", "34.5", "0.0", "7.4", "10.8", "39.0", "9.0", "17.0", "78.0", "35.0", "1010.8", "1007.4", "3.0", "2.0", "28.1", "33.5", "0"], ["115", "8.8", "24.6", "8.4", "12.2", "9.1", "28.0", "13.0", "9.0", "61.0", "31.0", "1013.0", "1010.8", "6.0", "6.0", "14.7", "21.4", "0"], ["116", "6.2", "17.4", "0.2", "2.0", "9.6", "20.0", "11.0", "11.0", "73.0", "48.0", "1030.7", "1026.8", "1.0", "1.0", "10.5", "16.1", "0"], ["117", "4.1", "14.2", "0.2", "1.2", "7.3", "24.0", "2.0", "11.0", "86.0", "46.0", "1030.5", "1029.1", "7.0", "4.0", "7.5", "13.5", "0"], ["118", "22.2", "30.2", "0.0", "4.0", "9.2", "30.0", "7.0", "17.0", "74.0", "56.0", "1015.0", "1012.1", "6.0", "1.0", "24.5", "28.6", "0"], ["124", "14.3", "38.7", "0.0", "8.0", "12.5", "67.0", "22.0", "33.0", "18.0", "6.0", "1008.0", "1000.7", "2.0", "5.0", "26.9", "37.2", "1"], ["125", "23.1", "36.5", "0.0", "11.8", "4.7", "57.0", "19.0", "37.0", "22.0", "46.0", "1007.8", "1010.8", "3.0", "8.0", "32.8", "27.6", "1"]], "shape": {"columns": 17, "rows": 39716}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>Rainfall</th>\n", "      <th>Evaporation</th>\n", "      <th><PERSON></th>\n", "      <th>WindGustSpeed</th>\n", "      <th>WindSpeed9am</th>\n", "      <th>WindSpeed3pm</th>\n", "      <th>Humidity9am</th>\n", "      <th>Humidity3pm</th>\n", "      <th>Pressure9am</th>\n", "      <th>Pressure3pm</th>\n", "      <th>Cloud9am</th>\n", "      <th>Cloud3pm</th>\n", "      <th>Temp9am</th>\n", "      <th>Temp3pm</th>\n", "      <th>RainTomorrow</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8.7</td>\n", "      <td>17.2</td>\n", "      <td>0.0</td>\n", "      <td>3.8</td>\n", "      <td>9.9</td>\n", "      <td>54.0</td>\n", "      <td>17.0</td>\n", "      <td>26.0</td>\n", "      <td>54.0</td>\n", "      <td>28.0</td>\n", "      <td>1019.4</td>\n", "      <td>1016.3</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>12.2</td>\n", "      <td>16.6</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>15.2</td>\n", "      <td>22.2</td>\n", "      <td>7.2</td>\n", "      <td>5.2</td>\n", "      <td>3.3</td>\n", "      <td>78.0</td>\n", "      <td>33.0</td>\n", "      <td>28.0</td>\n", "      <td>68.0</td>\n", "      <td>91.0</td>\n", "      <td>1001.5</td>\n", "      <td>1000.1</td>\n", "      <td>7.0</td>\n", "      <td>8.0</td>\n", "      <td>20.1</td>\n", "      <td>16.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>-0.3</td>\n", "      <td>14.2</td>\n", "      <td>0.0</td>\n", "      <td>3.6</td>\n", "      <td>9.7</td>\n", "      <td>43.0</td>\n", "      <td>13.0</td>\n", "      <td>28.0</td>\n", "      <td>67.0</td>\n", "      <td>50.0</td>\n", "      <td>1019.0</td>\n", "      <td>1015.5</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>9.2</td>\n", "      <td>13.7</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>9.8</td>\n", "      <td>20.1</td>\n", "      <td>0.0</td>\n", "      <td>4.8</td>\n", "      <td>8.3</td>\n", "      <td>41.0</td>\n", "      <td>17.0</td>\n", "      <td>20.0</td>\n", "      <td>60.0</td>\n", "      <td>47.0</td>\n", "      <td>1015.3</td>\n", "      <td>1013.7</td>\n", "      <td>6.0</td>\n", "      <td>7.0</td>\n", "      <td>15.1</td>\n", "      <td>18.9</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>19.1</td>\n", "      <td>27.2</td>\n", "      <td>0.0</td>\n", "      <td>6.4</td>\n", "      <td>3.2</td>\n", "      <td>44.0</td>\n", "      <td>19.0</td>\n", "      <td>30.0</td>\n", "      <td>69.0</td>\n", "      <td>63.0</td>\n", "      <td>1016.2</td>\n", "      <td>1013.5</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>23.2</td>\n", "      <td>25.1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101813</th>\n", "      <td>12.0</td>\n", "      <td>27.7</td>\n", "      <td>0.0</td>\n", "      <td>2.6</td>\n", "      <td>8.8</td>\n", "      <td>26.0</td>\n", "      <td>11.0</td>\n", "      <td>9.0</td>\n", "      <td>73.0</td>\n", "      <td>37.0</td>\n", "      <td>1013.0</td>\n", "      <td>1009.2</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>15.5</td>\n", "      <td>26.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101814</th>\n", "      <td>9.7</td>\n", "      <td>19.5</td>\n", "      <td>16.4</td>\n", "      <td>2.8</td>\n", "      <td>2.7</td>\n", "      <td>80.0</td>\n", "      <td>13.0</td>\n", "      <td>24.0</td>\n", "      <td>93.0</td>\n", "      <td>62.0</td>\n", "      <td>1009.4</td>\n", "      <td>1005.1</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>13.2</td>\n", "      <td>18.2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101815</th>\n", "      <td>17.1</td>\n", "      <td>36.9</td>\n", "      <td>0.2</td>\n", "      <td>5.8</td>\n", "      <td>9.7</td>\n", "      <td>37.0</td>\n", "      <td>11.0</td>\n", "      <td>19.0</td>\n", "      <td>52.0</td>\n", "      <td>20.0</td>\n", "      <td>1016.9</td>\n", "      <td>1014.8</td>\n", "      <td>3.0</td>\n", "      <td>7.0</td>\n", "      <td>25.2</td>\n", "      <td>36.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101816</th>\n", "      <td>0.6</td>\n", "      <td>12.8</td>\n", "      <td>0.0</td>\n", "      <td>0.4</td>\n", "      <td>6.1</td>\n", "      <td>26.0</td>\n", "      <td>9.0</td>\n", "      <td>11.0</td>\n", "      <td>85.0</td>\n", "      <td>53.0</td>\n", "      <td>1033.1</td>\n", "      <td>1030.4</td>\n", "      <td>5.0</td>\n", "      <td>3.0</td>\n", "      <td>8.5</td>\n", "      <td>12.4</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101818</th>\n", "      <td>7.6</td>\n", "      <td>20.4</td>\n", "      <td>0.0</td>\n", "      <td>2.4</td>\n", "      <td>9.2</td>\n", "      <td>30.0</td>\n", "      <td>9.0</td>\n", "      <td>17.0</td>\n", "      <td>64.0</td>\n", "      <td>35.0</td>\n", "      <td>1027.7</td>\n", "      <td>1024.2</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>12.1</td>\n", "      <td>19.9</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>39716 rows × 17 columns</p>\n", "</div>"], "text/plain": ["        MinTemp  MaxTemp  Rainfall  Evaporation  Sunshine  WindGustSpeed  \\\n", "0           8.7     17.2       0.0          3.8       9.9           54.0   \n", "2          15.2     22.2       7.2          5.2       3.3           78.0   \n", "6          -0.3     14.2       0.0          3.6       9.7           43.0   \n", "13          9.8     20.1       0.0          4.8       8.3           41.0   \n", "14         19.1     27.2       0.0          6.4       3.2           44.0   \n", "...         ...      ...       ...          ...       ...            ...   \n", "101813     12.0     27.7       0.0          2.6       8.8           26.0   \n", "101814      9.7     19.5      16.4          2.8       2.7           80.0   \n", "101815     17.1     36.9       0.2          5.8       9.7           37.0   \n", "101816      0.6     12.8       0.0          0.4       6.1           26.0   \n", "101818      7.6     20.4       0.0          2.4       9.2           30.0   \n", "\n", "        WindSpeed9am  WindSpeed3pm  Humidity9am  Humidity3pm  Pressure9am  \\\n", "0               17.0          26.0         54.0         28.0       1019.4   \n", "2               33.0          28.0         68.0         91.0       1001.5   \n", "6               13.0          28.0         67.0         50.0       1019.0   \n", "13              17.0          20.0         60.0         47.0       1015.3   \n", "14              19.0          30.0         69.0         63.0       1016.2   \n", "...              ...           ...          ...          ...          ...   \n", "101813          11.0           9.0         73.0         37.0       1013.0   \n", "101814          13.0          24.0         93.0         62.0       1009.4   \n", "101815          11.0          19.0         52.0         20.0       1016.9   \n", "101816           9.0          11.0         85.0         53.0       1033.1   \n", "101818           9.0          17.0         64.0         35.0       1027.7   \n", "\n", "        Pressure3pm  Cloud9am  Cloud3pm  Temp9am  Temp3pm  RainTomorrow  \n", "0            1016.3       2.0       2.0     12.2     16.6             0  \n", "2            1000.1       7.0       8.0     20.1     16.5             1  \n", "6            1015.5       2.0       0.0      9.2     13.7             0  \n", "13           1013.7       6.0       7.0     15.1     18.9             0  \n", "14           1013.5       7.0       7.0     23.2     25.1             1  \n", "...             ...       ...       ...      ...      ...           ...  \n", "101813       1009.2       4.0       1.0     15.5     26.5             0  \n", "101814       1005.1       7.0       6.0     13.2     18.2             1  \n", "101815       1014.8       3.0       7.0     25.2     36.5             0  \n", "101816       1030.4       5.0       3.0      8.5     12.4             0  \n", "101818       1024.2       0.0       1.0     12.1     19.9             0  \n", "\n", "[39716 rows x 17 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["numeric_df"]}, {"cell_type": "code", "execution_count": 28, "id": "aef283de", "metadata": {}, "outputs": [], "source": ["x = numeric_df.drop('RainTomorrow', axis=1)\n", "y = numeric_df['RainTomorrow'] "]}, {"cell_type": "code", "execution_count": 31, "id": "fa8dae16", "metadata": {}, "outputs": [], "source": ["x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.3, random_state=42)"]}, {"cell_type": "code", "execution_count": 32, "id": "33de1d16", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "MinTemp", "rawType": "float64", "type": "float"}, {"name": "MaxTemp", "rawType": "float64", "type": "float"}, {"name": "Rainfall", "rawType": "float64", "type": "float"}, {"name": "Evaporation", "rawType": "float64", "type": "float"}, {"name": "Sunshine", "rawType": "float64", "type": "float"}, {"name": "WindGustSpeed", "rawType": "float64", "type": "float"}, {"name": "WindSpeed9am", "rawType": "float64", "type": "float"}, {"name": "WindSpeed3pm", "rawType": "float64", "type": "float"}, {"name": "Humidity9am", "rawType": "float64", "type": "float"}, {"name": "Humidity3pm", "rawType": "float64", "type": "float"}, {"name": "Pressure9am", "rawType": "float64", "type": "float"}, {"name": "Pressure3pm", "rawType": "float64", "type": "float"}, {"name": "Cloud9am", "rawType": "float64", "type": "float"}, {"name": "Cloud3pm", "rawType": "float64", "type": "float"}, {"name": "Temp9am", "rawType": "float64", "type": "float"}, {"name": "Temp3pm", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "2eed1722-2d7f-4667-b80a-9cbc606f4fe6", "rows": [["9083", "5.9", "24.6", "0.0", "2.6", "9.4", "48.0", "9.0", "26.0", "63.0", "24.0", "1015.2", "1012.1", "2.0", "6.0", "15.6", "23.9"], ["35843", "9.0", "12.7", "0.0", "1.0", "1.8", "37.0", "17.0", "17.0", "86.0", "82.0", "1032.6", "1031.1", "7.0", "5.0", "10.5", "12.3"], ["72690", "0.9", "11.3", "0.0", "1.4", "1.5", "28.0", "11.0", "15.0", "93.0", "69.0", "1021.6", "1017.2", "7.0", "7.0", "4.4", "10.4"], ["66147", "20.2", "28.7", "0.0", "12.8", "6.2", "46.0", "26.0", "22.0", "52.0", "23.0", "1017.1", "1016.0", "6.0", "2.0", "20.6", "27.9"], ["72385", "20.0", "28.2", "0.2", "3.8", "6.8", "37.0", "15.0", "26.0", "63.0", "61.0", "1015.2", "1012.0", "2.0", "6.0", "26.1", "26.3"], ["72878", "15.2", "19.4", "0.2", "4.2", "7.3", "52.0", "26.0", "22.0", "57.0", "65.0", "1014.8", "1013.5", "2.0", "2.0", "18.2", "18.8"], ["73622", "22.5", "30.7", "0.6", "6.8", "5.7", "33.0", "13.0", "20.0", "79.0", "48.0", "1017.9", "1013.6", "7.0", "2.0", "25.6", "29.6"], ["2670", "12.5", "17.5", "8.8", "4.0", "1.3", "52.0", "20.0", "33.0", "76.0", "73.0", "1008.0", "1010.5", "6.0", "6.0", "13.9", "16.6"], ["52951", "4.7", "20.3", "0.0", "3.0", "9.7", "41.0", "19.0", "15.0", "51.0", "34.0", "1018.1", "1014.1", "1.0", "2.0", "12.4", "20.0"], ["61396", "24.6", "31.6", "25.0", "4.6", "1.9", "59.0", "20.0", "31.0", "83.0", "75.0", "1008.9", "1005.9", "8.0", "7.0", "28.4", "30.6"], ["35829", "13.5", "19.0", "0.2", "4.6", "2.1", "57.0", "11.0", "31.0", "80.0", "89.0", "1012.1", "1009.9", "8.0", "6.0", "17.2", "19.0"], ["39258", "8.6", "25.4", "0.0", "8.0", "13.6", "33.0", "13.0", "22.0", "68.0", "48.0", "1016.9", "1014.0", "1.0", "1.0", "15.1", "23.8"], ["93397", "16.9", "29.8", "0.0", "12.0", "10.9", "54.0", "35.0", "37.0", "52.0", "43.0", "1023.9", "1020.8", "5.0", "5.0", "25.8", "28.2"], ["49094", "24.4", "32.5", "0.0", "5.6", "8.3", "39.0", "13.0", "20.0", "72.0", "79.0", "1008.7", "1006.3", "6.0", "7.0", "29.1", "29.4"], ["85242", "13.1", "19.3", "6.2", "1.6", "7.3", "65.0", "22.0", "33.0", "70.0", "62.0", "1005.0", "1002.6", "5.0", "4.0", "15.7", "18.7"], ["94209", "2.0", "18.1", "0.0", "4.8", "10.7", "37.0", "13.0", "20.0", "76.0", "39.0", "1019.3", "1015.7", "1.0", "1.0", "9.6", "17.5"], ["97355", "8.1", "15.8", "1.4", "2.4", "5.4", "50.0", "11.0", "24.0", "88.0", "51.0", "1020.9", "1018.4", "7.0", "7.0", "9.3", "14.8"], ["2138", "6.7", "14.1", "0.2", "0.6", "0.0", "24.0", "9.0", "11.0", "93.0", "56.0", "1028.2", "1024.8", "7.0", "7.0", "7.5", "13.5"], ["11593", "16.8", "25.1", "1.6", "4.2", "5.7", "26.0", "7.0", "4.0", "74.0", "52.0", "1023.1", "1020.5", "6.0", "6.0", "19.6", "23.4"], ["82292", "7.0", "26.5", "0.0", "1.8", "9.2", "31.0", "17.0", "13.0", "81.0", "38.0", "1028.6", "1024.6", "1.0", "1.0", "12.9", "25.3"], ["42588", "13.2", "19.9", "1.6", "0.0", "9.1", "37.0", "22.0", "19.0", "65.0", "69.0", "1017.9", "1016.2", "3.0", "1.0", "17.1", "18.8"], ["32264", "14.3", "36.7", "0.0", "8.4", "3.8", "44.0", "9.0", "9.0", "54.0", "22.0", "1006.5", "1003.0", "3.0", "6.0", "23.0", "35.3"], ["25708", "7.5", "22.0", "0.0", "4.8", "10.1", "22.0", "4.0", "13.0", "75.0", "37.0", "1018.0", "1014.5", "1.0", "0.0", "13.7", "21.2"], ["75729", "10.8", "24.9", "0.0", "4.4", "9.7", "39.0", "11.0", "22.0", "59.0", "41.0", "1016.7", "1013.9", "7.0", "5.0", "15.7", "22.8"], ["63648", "10.8", "22.7", "0.0", "5.4", "11.2", "41.0", "17.0", "22.0", "62.0", "50.0", "1011.3", "1012.2", "7.0", "4.0", "15.8", "20.0"], ["92194", "11.7", "18.2", "0.0", "4.0", "0.3", "41.0", "9.0", "13.0", "69.0", "76.0", "1018.7", "1013.7", "8.0", "8.0", "16.1", "16.4"], ["3562", "7.1", "20.7", "0.0", "2.2", "5.5", "24.0", "7.0", "6.0", "81.0", "43.0", "1019.9", "1016.3", "2.0", "6.0", "10.6", "20.2"], ["14342", "6.9", "23.2", "0.0", "6.0", "12.2", "48.0", "13.0", "28.0", "59.0", "43.0", "1011.0", "1009.2", "4.0", "1.0", "16.0", "22.2"], ["31992", "17.9", "22.0", "0.0", "6.2", "11.3", "35.0", "19.0", "19.0", "68.0", "64.0", "1017.8", "1016.9", "1.0", "2.0", "19.8", "20.7"], ["16428", "18.4", "37.5", "0.0", "8.2", "11.4", "52.0", "13.0", "19.0", "52.0", "27.0", "1018.8", "1015.3", "4.0", "5.0", "26.1", "36.1"], ["35463", "18.4", "29.3", "0.0", "9.2", "11.6", "33.0", "13.0", "20.0", "55.0", "59.0", "1016.0", "1012.2", "1.0", "1.0", "26.4", "27.4"], ["18346", "22.8", "30.1", "7.0", "7.6", "1.4", "59.0", "20.0", "35.0", "83.0", "65.0", "1008.7", "1006.8", "7.0", "7.0", "25.8", "28.3"], ["25340", "11.7", "17.7", "6.4", "3.4", "2.8", "43.0", "26.0", "22.0", "69.0", "56.0", "1017.0", "1016.8", "8.0", "6.0", "13.4", "16.3"], ["54449", "15.5", "27.1", "0.0", "5.8", "9.7", "35.0", "19.0", "24.0", "72.0", "48.0", "1017.8", "1014.2", "3.0", "1.0", "21.3", "25.1"], ["31317", "20.4", "30.6", "0.0", "10.2", "12.9", "33.0", "7.0", "15.0", "43.0", "52.0", "1014.7", "1012.5", "1.0", "1.0", "28.3", "28.9"], ["20802", "3.8", "14.2", "1.8", "2.8", "9.0", "48.0", "15.0", "33.0", "76.0", "63.0", "1028.8", "1028.4", "6.0", "7.0", "9.3", "12.3"], ["37773", "3.8", "18.0", "0.4", "1.8", "2.3", "31.0", "9.0", "17.0", "91.0", "83.0", "1016.4", "1013.3", "4.0", "8.0", "8.1", "13.7"], ["78692", "14.2", "22.6", "0.2", "0.2", "5.6", "37.0", "9.0", "11.0", "89.0", "69.0", "1014.0", "1011.0", "4.0", "6.0", "16.6", "22.2"], ["83799", "11.4", "20.6", "1.6", "8.2", "6.4", "46.0", "19.0", "31.0", "57.0", "62.0", "1005.1", "1006.4", "4.0", "7.0", "15.3", "17.4"], ["68067", "6.2", "21.0", "0.0", "0.8", "7.0", "37.0", "11.0", "9.0", "81.0", "49.0", "1027.5", "1025.6", "1.0", "3.0", "14.3", "20.4"], ["28943", "8.9", "17.5", "0.0", "5.9", "8.4", "41.0", "17.0", "20.0", "68.0", "37.0", "1018.8", "1019.0", "1.0", "5.0", "11.8", "16.3"], ["60471", "9.7", "30.3", "0.0", "6.8", "13.5", "26.0", "6.0", "9.0", "60.0", "30.0", "1023.4", "1019.6", "1.0", "0.0", "16.5", "26.8"], ["44662", "14.1", "20.9", "22.8", "4.6", "3.6", "61.0", "39.0", "37.0", "81.0", "44.0", "1005.2", "1007.6", "7.0", "6.0", "16.1", "19.4"], ["87105", "13.4", "24.5", "0.0", "3.6", "1.3", "39.0", "15.0", "13.0", "36.0", "42.0", "1020.0", "1016.6", "7.0", "7.0", "20.4", "23.3"], ["22915", "13.3", "22.2", "0.0", "2.8", "7.3", "46.0", "17.0", "19.0", "71.0", "35.0", "1006.9", "1001.9", "2.0", "6.0", "16.1", "21.5"], ["65479", "9.0", "14.6", "0.0", "2.2", "2.0", "28.0", "13.0", "13.0", "71.0", "66.0", "1015.4", "1015.8", "7.0", "7.0", "12.4", "13.5"], ["86351", "19.5", "23.2", "0.0", "3.6", "2.5", "48.0", "13.0", "20.0", "72.0", "71.0", "1011.0", "1012.2", "8.0", "7.0", "22.4", "21.9"], ["90378", "20.4", "28.9", "0.0", "6.2", "11.2", "52.0", "11.0", "24.0", "77.0", "62.0", "1013.7", "1011.4", "3.0", "5.0", "25.3", "28.7"], ["70204", "2.1", "18.7", "0.0", "1.8", "9.4", "22.0", "11.0", "13.0", "77.0", "43.0", "1036.8", "1033.2", "0.0", "1.0", "10.4", "17.9"], ["24948", "9.2", "15.2", "9.8", "1.8", "6.9", "67.0", "26.0", "35.0", "67.0", "42.0", "1009.2", "1012.6", "6.0", "4.0", "12.2", "14.5"]], "shape": {"columns": 16, "rows": 27801}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>Rainfall</th>\n", "      <th>Evaporation</th>\n", "      <th><PERSON></th>\n", "      <th>WindGustSpeed</th>\n", "      <th>WindSpeed9am</th>\n", "      <th>WindSpeed3pm</th>\n", "      <th>Humidity9am</th>\n", "      <th>Humidity3pm</th>\n", "      <th>Pressure9am</th>\n", "      <th>Pressure3pm</th>\n", "      <th>Cloud9am</th>\n", "      <th>Cloud3pm</th>\n", "      <th>Temp9am</th>\n", "      <th>Temp3pm</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9083</th>\n", "      <td>5.9</td>\n", "      <td>24.6</td>\n", "      <td>0.0</td>\n", "      <td>2.6</td>\n", "      <td>9.4</td>\n", "      <td>48.0</td>\n", "      <td>9.0</td>\n", "      <td>26.0</td>\n", "      <td>63.0</td>\n", "      <td>24.0</td>\n", "      <td>1015.2</td>\n", "      <td>1012.1</td>\n", "      <td>2.0</td>\n", "      <td>6.0</td>\n", "      <td>15.6</td>\n", "      <td>23.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35843</th>\n", "      <td>9.0</td>\n", "      <td>12.7</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>1.8</td>\n", "      <td>37.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>86.0</td>\n", "      <td>82.0</td>\n", "      <td>1032.6</td>\n", "      <td>1031.1</td>\n", "      <td>7.0</td>\n", "      <td>5.0</td>\n", "      <td>10.5</td>\n", "      <td>12.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72690</th>\n", "      <td>0.9</td>\n", "      <td>11.3</td>\n", "      <td>0.0</td>\n", "      <td>1.4</td>\n", "      <td>1.5</td>\n", "      <td>28.0</td>\n", "      <td>11.0</td>\n", "      <td>15.0</td>\n", "      <td>93.0</td>\n", "      <td>69.0</td>\n", "      <td>1021.6</td>\n", "      <td>1017.2</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>4.4</td>\n", "      <td>10.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66147</th>\n", "      <td>20.2</td>\n", "      <td>28.7</td>\n", "      <td>0.0</td>\n", "      <td>12.8</td>\n", "      <td>6.2</td>\n", "      <td>46.0</td>\n", "      <td>26.0</td>\n", "      <td>22.0</td>\n", "      <td>52.0</td>\n", "      <td>23.0</td>\n", "      <td>1017.1</td>\n", "      <td>1016.0</td>\n", "      <td>6.0</td>\n", "      <td>2.0</td>\n", "      <td>20.6</td>\n", "      <td>27.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72385</th>\n", "      <td>20.0</td>\n", "      <td>28.2</td>\n", "      <td>0.2</td>\n", "      <td>3.8</td>\n", "      <td>6.8</td>\n", "      <td>37.0</td>\n", "      <td>15.0</td>\n", "      <td>26.0</td>\n", "      <td>63.0</td>\n", "      <td>61.0</td>\n", "      <td>1015.2</td>\n", "      <td>1012.0</td>\n", "      <td>2.0</td>\n", "      <td>6.0</td>\n", "      <td>26.1</td>\n", "      <td>26.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16158</th>\n", "      <td>13.9</td>\n", "      <td>23.2</td>\n", "      <td>0.0</td>\n", "      <td>2.8</td>\n", "      <td>9.6</td>\n", "      <td>31.0</td>\n", "      <td>9.0</td>\n", "      <td>22.0</td>\n", "      <td>68.0</td>\n", "      <td>59.0</td>\n", "      <td>1030.8</td>\n", "      <td>1027.5</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>20.1</td>\n", "      <td>22.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29115</th>\n", "      <td>7.8</td>\n", "      <td>20.5</td>\n", "      <td>0.4</td>\n", "      <td>1.8</td>\n", "      <td>4.1</td>\n", "      <td>43.0</td>\n", "      <td>20.0</td>\n", "      <td>17.0</td>\n", "      <td>79.0</td>\n", "      <td>53.0</td>\n", "      <td>1012.2</td>\n", "      <td>1009.4</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>12.5</td>\n", "      <td>18.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97860</th>\n", "      <td>3.7</td>\n", "      <td>13.7</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>8.3</td>\n", "      <td>24.0</td>\n", "      <td>9.0</td>\n", "      <td>11.0</td>\n", "      <td>83.0</td>\n", "      <td>62.0</td>\n", "      <td>1035.9</td>\n", "      <td>1034.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>9.6</td>\n", "      <td>12.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2158</th>\n", "      <td>15.7</td>\n", "      <td>32.7</td>\n", "      <td>0.0</td>\n", "      <td>16.0</td>\n", "      <td>13.3</td>\n", "      <td>52.0</td>\n", "      <td>28.0</td>\n", "      <td>24.0</td>\n", "      <td>51.0</td>\n", "      <td>12.0</td>\n", "      <td>1019.7</td>\n", "      <td>1018.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18.7</td>\n", "      <td>30.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40597</th>\n", "      <td>6.2</td>\n", "      <td>15.6</td>\n", "      <td>3.6</td>\n", "      <td>2.0</td>\n", "      <td>9.4</td>\n", "      <td>35.0</td>\n", "      <td>15.0</td>\n", "      <td>15.0</td>\n", "      <td>61.0</td>\n", "      <td>40.0</td>\n", "      <td>1023.5</td>\n", "      <td>1022.9</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>11.4</td>\n", "      <td>15.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>27801 rows × 16 columns</p>\n", "</div>"], "text/plain": ["       MinTemp  MaxTemp  Rainfall  Evaporation  Sunshine  WindGustSpeed  \\\n", "9083       5.9     24.6       0.0          2.6       9.4           48.0   \n", "35843      9.0     12.7       0.0          1.0       1.8           37.0   \n", "72690      0.9     11.3       0.0          1.4       1.5           28.0   \n", "66147     20.2     28.7       0.0         12.8       6.2           46.0   \n", "72385     20.0     28.2       0.2          3.8       6.8           37.0   \n", "...        ...      ...       ...          ...       ...            ...   \n", "16158     13.9     23.2       0.0          2.8       9.6           31.0   \n", "29115      7.8     20.5       0.4          1.8       4.1           43.0   \n", "97860      3.7     13.7       0.0          1.0       8.3           24.0   \n", "2158      15.7     32.7       0.0         16.0      13.3           52.0   \n", "40597      6.2     15.6       3.6          2.0       9.4           35.0   \n", "\n", "       WindSpeed9am  WindSpeed3pm  Humidity9am  Humidity3pm  Pressure9am  \\\n", "9083            9.0          26.0         63.0         24.0       1015.2   \n", "35843          17.0          17.0         86.0         82.0       1032.6   \n", "72690          11.0          15.0         93.0         69.0       1021.6   \n", "66147          26.0          22.0         52.0         23.0       1017.1   \n", "72385          15.0          26.0         63.0         61.0       1015.2   \n", "...             ...           ...          ...          ...          ...   \n", "16158           9.0          22.0         68.0         59.0       1030.8   \n", "29115          20.0          17.0         79.0         53.0       1012.2   \n", "97860           9.0          11.0         83.0         62.0       1035.9   \n", "2158           28.0          24.0         51.0         12.0       1019.7   \n", "40597          15.0          15.0         61.0         40.0       1023.5   \n", "\n", "       Pressure3pm  Cloud9am  Cloud3pm  Temp9am  Temp3pm  \n", "9083        1012.1       2.0       6.0     15.6     23.9  \n", "35843       1031.1       7.0       5.0     10.5     12.3  \n", "72690       1017.2       7.0       7.0      4.4     10.4  \n", "66147       1016.0       6.0       2.0     20.6     27.9  \n", "72385       1012.0       2.0       6.0     26.1     26.3  \n", "...            ...       ...       ...      ...      ...  \n", "16158       1027.5       3.0       3.0     20.1     22.1  \n", "29115       1009.4       7.0       6.0     12.5     18.8  \n", "97860       1034.0       2.0       4.0      9.6     12.8  \n", "2158        1018.0       0.0       0.0     18.7     30.5  \n", "40597       1022.9       1.0       1.0     11.4     15.0  \n", "\n", "[27801 rows x 16 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["x_train"]}, {"cell_type": "code", "execution_count": 40, "id": "8895bcc8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\exchange\\Lib\\site-packages\\sklearn\\linear_model\\_logistic.py:465: ConvergenceWarning: lbfgs failed to converge (status=1):\n", "STOP: TOTAL NO. OF ITERATIONS REACHED LIMIT.\n", "\n", "Increase the number of iterations (max_iter) or scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  n_iter_i = _check_optimize_result(\n"]}], "source": ["lr = LogisticRegression(random_state=42, max_iter=1000)\n", "\n", "# Train the logistic regression model\n", "lr.fit(x_train, y_train)\n", "y_pred_lr = lr.predict(x_test)"]}, {"cell_type": "code", "execution_count": 41, "id": "ab45c804", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8531263113722198"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["accuracy_score(y_test, y_pred_lr)"]}, {"cell_type": "code", "execution_count": null, "id": "854cd1e5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "exchange", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}