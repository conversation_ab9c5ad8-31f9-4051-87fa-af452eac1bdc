import pandas as pd
import matplotlib.pyplot as plt
from sklearn.tree import DecisionTreeClassifier, plot_tree
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score


file_path = "australian-weather-prediction\weatherAUS_train.csv"
df = pd.read_csv(file_path)


df.head()

print(df.describe())

df.columns

plt.figure(figsize=(24,15))

plt.subplot(4, 4, 1)
fig = df.MinTemp.hist(bins=50)
fig.set_xlabel('MinTemp')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 2)
fig = df.MaxTemp.hist(bins=50)
fig.set_xlabel('MaxTemp')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 3)
fig = df.Rainfall.hist(bins=50)
fig.set_xlabel('Rainfall')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 4)
fig = df.WindGustSpeed.hist(bins=50)
fig.set_xlabel('WindGustSpeed')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 5)
fig = df.WindSpeed9am.hist(bins=50)
fig.set_xlabel('WindSpeed9am')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 6)
fig = df.WindSpeed3pm.hist(bins=50)
fig.set_xlabel('WindSpeed3pm')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 7)
fig = df.Humidity9am.hist(bins=50)
fig.set_xlabel('Humidity9am')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 8)
fig = df.Humidity3pm.hist(bins=50)
fig.set_xlabel('Humidity3pm')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 9)
fig = df.Pressure9am.hist(bins=50)
fig.set_xlabel('Pressure9am')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 10)
fig = df.Pressure3pm.hist(bins=50)
fig.set_xlabel('Pressure3pm')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 11)
fig = df.Temp9am.hist(bins=50)
fig.set_xlabel('Temp9am')
fig.set_ylabel('RainTomorrow')

plt.subplot(4, 4, 12)
fig = df.Temp3pm.hist(bins=50)
fig.set_xlabel('Temp3pm')
fig.set_ylabel('RainTomorrow')

df.isnull().sum()

df_clean = df.dropna()
print(df_clean.isnull().sum())
df_clean.shape

df.dtypes

numeric_df = df_clean.select_dtypes(exclude=['object'])
numeric_df.dtypes

numeric_df

x = numeric_df.drop('RainTomorrow', axis=1)
y = numeric_df['RainTomorrow'] 

x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.3, random_state=42)

x_train

dt_model = DecisionTreeClassifier(max_depth=5, random_state=42)
dt_model.fit(x_train, y_train)

y_pred = dt_model.predict(x_test)

y_pred

y_test

accuracy_score(y_test, y_pred)


plt.figure(figsize=(20, 10))
plot_tree(dt_model, filled=True, feature_names=x.columns, class_names=['No Rain', 'Rain'], rounded=True)
plt.title("Decision Tree for Rain Prediction")
plt.show()

feature_importance = pd.DataFrame({
    'Feature': x.columns,
    'Importance': dt_model.feature_importances_
}).sort_values('Importance', ascending=False)

print("\nFeature Importance:")
print(feature_importance)

plt.figure(figsize=(10, 6))
plt.barh(feature_importance['Feature'], feature_importance['Importance'])
plt.xlabel('Importance')
plt.title('Feature Importance for Rain Prediction')
plt.gca().invert_yaxis()  # Display the most important at the top
plt.show()