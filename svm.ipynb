{"cells": [{"cell_type": "code", "execution_count": 14, "id": "cfd4712f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.svm import SVC\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report\n"]}, {"cell_type": "code", "execution_count": 8, "id": "46de7720", "metadata": {}, "outputs": [], "source": ["file_path = \"australian-weather-prediction\\weatherAUS_train.csv\"\n", "df = pd.read_csv(file_path)"]}, {"cell_type": "code", "execution_count": 9, "id": "2160df01", "metadata": {}, "outputs": [], "source": ["df_clean = df.dropna()\n", "numeric_df = df_clean.select_dtypes(exclude=['object'])"]}, {"cell_type": "code", "execution_count": 10, "id": "f0a8eb3e", "metadata": {}, "outputs": [], "source": ["x = numeric_df.drop('RainTomorrow', axis=1)\n", "y = numeric_df['RainTomorrow'] "]}, {"cell_type": "code", "execution_count": 11, "id": "341028cc", "metadata": {}, "outputs": [], "source": ["x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.3, random_state=42)"]}, {"cell_type": "code", "execution_count": 18, "id": "761b0ef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8572387746537977"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create the SVM\n", "svm_model = Pipeline([\n", "    ('scaler', StandardScaler()),\n", "    ('svm', SVC(random_state=42))\n", "])\n", "\n", "# Train a basic SVM model with default parameters (RBF kernel)\n", "svm_model.fit(x_train, y_train)\n", "y_pred = svm_model.predict(x_test)\n", "accuracy_score(y_test, y_pred)"]}], "metadata": {"kernelspec": {"display_name": "exchange", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}